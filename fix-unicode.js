const fs = require('fs');

// Read the file
const filePath = 'data/blog/toc/turing-machine.mdx';
let content = fs.readFileSync(filePath, 'utf8');

// Replace all Unicode subscripts with regular characters
content = content.replace(/q₀/g, 'q0');
content = content.replace(/q₁/g, 'q1');
content = content.replace(/qₐ/g, 'qa');
content = content.replace(/qᵣ/g, 'qr');

// Write the file back
fs.writeFileSync(filePath, content, 'utf8');

console.log('Unicode characters fixed in turing-machine.mdx');
